import { useState, useMemo, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, Search, Edit, Trash2, AlertTriangle, Package } from 'lucide-react';
import { useProducts, useProductCategories, useCreateProduct, useUpdateProduct, useDeleteProduct } from '@/hooks/useApi';
import { useToast } from '@/components/ui/use-toast';
import { usePermissions, ProtectedComponent } from '@/contexts/PermissionsContext';
import { Product } from '@/types';
import { formatCurrency } from '@/lib/currency';

const Products = () => {
  // Hooks pour les données API
  const { data: productsResponse, isLoading: productsLoading, error: productsError } = useProducts();

  console.log('🔍 Products page state:', {
    productsResponse,
    productsLoading,
    productsError,
  });

  // Log détaillé des produits reçus
  if (productsResponse?.results) {
    console.log('🔍 Premier produit reçu:', productsResponse.results[0]);
    console.log('🔍 Clés du premier produit:', Object.keys(productsResponse.results[0] || {}));
  }
  const { data: categoriesResponse, isLoading: categoriesLoading } = useProductCategories();
  const createProduct = useCreateProduct();
  const updateProduct = useUpdateProduct();
  const deleteProduct = useDeleteProduct();

  // État local
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Toutes');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const { toast } = useToast();
  const { canManageProducts, hasPermission } = usePermissions();

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    purchase_price: '',
    selling_price: '',
    current_stock: '',
    minimum_stock: '',
    unit: 'unité',
    description: ''
  });

  // Extraire les données de la réponse paginée
  const products = productsResponse?.results || (Array.isArray(productsResponse) ? productsResponse : []);
  const categories = categoriesResponse?.results || (Array.isArray(categoriesResponse) ? categoriesResponse : []);

  // Filtrage des produits
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    return products.filter((product: Product) => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      const productCategoryName = categories.find(c => c.id === product.category)?.name;
      const matchesCategory = selectedCategory === 'Toutes' || productCategoryName === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [products, searchTerm, selectedCategory]);

  // Liste des catégories avec "Toutes"
  const categoryOptions = useMemo(() => {
    const cats = ['Toutes'];
    if (categories) {
      cats.push(...categories.map((cat: any) => cat.name || cat));
    }
    return cats;
  }, [categories]);

  const unitMapping: { [key: string]: string } = {
    'piece': 'Pièce',
    'bouteille': 'Bouteille',
    'casier': 'Casier',
    'litre': 'Litre',
    'kg': 'Kilogramme',
    'portion': 'Portion',
  };

  const UNITS = Object.values(unitMapping);

  const resetForm = () => {
    setFormData({
      name: '', category: '', purchase_price: '', selling_price: '',
      current_stock: '', minimum_stock: '', unit: 'Pièce', description: ''
    });
    setEditingProduct(null);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    // Validation des champs obligatoires
    console.log('🔍 Données du formulaire avant validation:', formData);

    if (!formData.name || !formData.category || !formData.purchase_price || !formData.selling_price) {
      console.error('❌ Champs obligatoires manquants:', {
        name: formData.name,
        category: formData.category,
        purchase_price: formData.purchase_price,
        selling_price: formData.selling_price
      });
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires"
      });
      return;
    }

    const unitKey = Object.keys(unitMapping).find(key => unitMapping[key] === formData.unit) || formData.unit;

    const productData = {
      name: formData.name.trim(),
      category: parseInt(formData.category), // Convertir en entier
      purchase_price: parseFloat(formData.purchase_price),
      selling_price: parseFloat(formData.selling_price),
      current_stock: parseInt(formData.current_stock) || 0,
      minimum_stock: parseInt(formData.minimum_stock) || 5,
      unit: unitKey, // Utiliser la clé backend pour l'unité
      description: formData.description?.trim() || '',
      is_active: true
    };

    // Validation stricte des données
    console.log('🔍 Validation des données:');
    const validationErrors = [];

    if (!productData.name || productData.name.length === 0) {
      validationErrors.push('Nom vide');
    }
    if (isNaN(productData.category) || productData.category <= 0) {
      validationErrors.push(`Catégorie invalide: ${productData.category}`);
    }
    if (isNaN(productData.purchase_price) || productData.purchase_price < 0) {
      validationErrors.push(`Prix d'achat invalide: ${productData.purchase_price}`);
    }
    if (isNaN(productData.selling_price) || productData.selling_price < 0) {
      validationErrors.push(`Prix de vente invalide: ${productData.selling_price}`);
    }
    if (isNaN(productData.current_stock) || productData.current_stock < 0) {
      validationErrors.push(`Stock actuel invalide: ${productData.current_stock}`);
    }
    if (isNaN(productData.minimum_stock) || productData.minimum_stock < 0) {
      validationErrors.push(`Stock minimum invalide: ${productData.minimum_stock}`);
    }

    if (validationErrors.length > 0) {
      console.error('❌ Erreurs de validation:', validationErrors);
      toast({
        variant: "destructive",
        title: "Erreur de validation",
        description: `Données invalides: ${validationErrors.join(', ')}`
      });
      return;
    }

    console.log('✅ Validation réussie');

    // Validation côté client
    if (productData.selling_price <= productData.purchase_price) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Le prix de vente doit être supérieur au prix d'achat"
      });
      return;
    }
    console.log('🔍 Données à envoyer:', productData);
    console.log('🔍 Type de chaque champ:', {
      name: typeof productData.name,
      category: typeof productData.category,
      purchase_price: typeof productData.purchase_price,
      selling_price: typeof productData.selling_price,
      current_stock: typeof productData.current_stock,
      minimum_stock: typeof productData.minimum_stock,
      unit: typeof productData.unit,
      is_active: typeof productData.is_active
    });

    try {
      if (editingProduct) {
        console.log('🔍 Mise à jour du produit ID:', editingProduct.id);
        console.log('🔍 Type de l\'ID:', typeof editingProduct.id);
        console.log('🔍 URL de la requête:', `/api/products/${editingProduct.id}/`);
        await updateProduct.mutateAsync({ id: editingProduct.id.toString(), data: productData });
        toast({ title: "Produit mis à jour", description: "Le produit a été modifié avec succès." });
      } else {
        console.log('🔍 Création d\'un nouveau produit');
        await createProduct.mutateAsync(productData);
        toast({ title: "Produit créé", description: "Le nouveau produit a été ajouté avec succès." });
      }
      resetForm();
      setIsDialogOpen(false);
    } catch (error: any) {
      const errorData = error?.response?.data;
      console.error('❌ SERVER ERROR RESPONSE:', JSON.stringify(errorData, null, 2));

      let errorMessage = "La sauvegarde a échoué.";
      if (errorData) {
        // Tente d'extraire un message lisible, sinon affiche le JSON brut
        errorMessage = errorData.detail || Object.values(errorData).flat().join(' ') || JSON.stringify(errorData);
      }

      toast({
        variant: "destructive",
        title: "Erreur",
        description: errorMessage
      });
    }
  };

  const handleEdit = (product: Product) => {
    console.log('🔍 handleEdit - Produit reçu:', product);
    console.log('🔍 handleEdit - Type de product.category:', typeof product.category);
    console.log('🔍 handleEdit - Valeur de product.category:', product.category);
    console.log('🔍 handleEdit - Toutes les clés du produit:', Object.keys(product));

    setEditingProduct(product);
    const categoryId = categories.find(c => c.name === product.category)?.id;
    setFormData({
      name: product.name || '',
      category: categoryId ? categoryId.toString() : '',
      purchase_price: (product.purchase_price ?? '').toString(),
      selling_price: (product.selling_price ?? '').toString(),
      current_stock: (product.current_stock ?? '').toString(),
      minimum_stock: (product.minimum_stock ?? '').toString(),
      unit: unitMapping[product.unit] || 'Pièce', // Convertir la clé en nom d'affichage
      description: product.description || ''
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (productId: string) => {
    try {
      await deleteProduct.mutateAsync(productId);
      toast({ title: "Produit supprimé", description: "Le produit a été supprimé avec succès." });
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({ variant: "destructive", title: "Erreur", description: "La suppression a échoué." });
    }
  };

  const getStockStatus = (currentStock: number, minimumStock: number) => {
    if (currentStock <= 0) return { variant: 'destructive' as const, text: 'Rupture' };
    if (currentStock <= minimumStock) return { variant: 'secondary' as const, text: 'Stock bas' };
    return { variant: 'default' as const, text: 'En stock' };
  };

  if (productsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
          <p className="text-muted-foreground">Impossible de charger les produits.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestion des Produits</h1>
          <p className="text-muted-foreground">Gérez votre inventaire de produits.</p>
        </div>
        <ProtectedComponent permission="products.create">
          <Dialog open={isDialogOpen} onOpenChange={(isOpen) => {
            if (!isOpen) {
              resetForm();
            }
            setIsDialogOpen(isOpen);
          }}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                resetForm();
                if (categories && categories.length > 0) {
                  setFormData(prev => ({ ...prev, category: categories[0].id.toString() }));
                }
                setIsDialogOpen(true);
              }}>
                <Plus className="w-4 h-4 mr-2" />
                Ajouter un produit
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{editingProduct ? 'Modifier le produit' : 'Ajouter un produit'}</DialogTitle>
                <DialogDescription>{editingProduct ? 'Modifiez les informations.' : 'Remplissez les informations.'}</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4 pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nom</Label>
                    <Input id="name" value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Catégorie</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                      <SelectTrigger><SelectValue placeholder="Choisir..." /></SelectTrigger>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>Chargement...</SelectItem>
                        ) : (
                          categories.map((cat: any) => <SelectItem key={cat.id} value={cat.id.toString()}>{cat.name}</SelectItem>)
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="purchase_price">Prix d'achat</Label>
                    <Input id="purchase_price" type="number" value={formData.purchase_price} onChange={(e) => setFormData({...formData, purchase_price: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="selling_price">Prix de vente</Label>
                    <Input id="selling_price" type="number" value={formData.selling_price} onChange={(e) => setFormData({...formData, selling_price: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="current_stock">Stock actuel</Label>
                    <Input id="current_stock" type="number" value={formData.current_stock} onChange={(e) => setFormData({...formData, current_stock: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="minimum_stock">Stock minimum</Label>
                    <Input id="minimum_stock" type="number" value={formData.minimum_stock} onChange={(e) => setFormData({...formData, minimum_stock: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">Unité</Label>
                    <Input id="unit" value={formData.unit} onChange={(e) => setFormData({...formData, unit: e.target.value})} required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input id="description" value={formData.description} onChange={(e) => setFormData({...formData, description: e.target.value})} />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>Annuler</Button>
                  <Button type="submit" disabled={createProduct.isPending || updateProduct.isPending}>
                    {createProduct.isPending || updateProduct.isPending ? 'Sauvegarde...' : 'Sauvegarder'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </ProtectedComponent>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des Produits</CardTitle>
          <CardDescription>Recherchez, filtrez et gérez vos produits.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Rechercher..." className="pl-8" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[180px]"><SelectValue placeholder="Filtrer..." /></SelectTrigger>
              <SelectContent>
                {categoryOptions.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>

          {productsLoading ? (
            <div className="space-y-2 py-4">
              {[...Array(5)].map((_, i) => <Skeleton key={i} className="h-12 w-full" />)}
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-10">
              <Package className="h-12 w-12 text-muted-foreground mx-auto" />
              <h3 className="mt-4 text-lg font-semibold">Aucun produit trouvé</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm || selectedCategory !== 'Toutes' ? "Essayez d'ajuster votre recherche." : 'Ajoutez votre premier produit pour commencer.'}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Produit</TableHead>
                  <TableHead>Catégorie</TableHead>
                  <TableHead className="text-right">Prix Vente</TableHead>
                  <TableHead className="text-right">Stock</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product: Product) => {
                  const stockStatus = getStockStatus(product.current_stock, product.minimum_stock);
                  return (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.category}</TableCell>
                      <TableCell className="text-right">{formatCurrency(product.selling_price)}</TableCell>
                      <TableCell className="text-right">{product.current_stock} {product.unit}</TableCell>
                      <TableCell><Badge variant={stockStatus.variant}>{stockStatus.text}</Badge></TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1">
                          <ProtectedComponent permission="products.update">
                            <Button variant="ghost" size="icon" onClick={() => handleEdit(product)}><Edit className="h-4 w-4" /></Button>
                          </ProtectedComponent>
                          <ProtectedComponent permission="products.delete">
                            <Button variant="ghost" size="icon" onClick={() => handleDelete(product.id)} disabled={!hasPermission('products.delete')}> 
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </ProtectedComponent>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Products;
