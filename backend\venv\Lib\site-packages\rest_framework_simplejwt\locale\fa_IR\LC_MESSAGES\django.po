# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-06 12:57+0330\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language: fa_IR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "هدر اعتبارسنجی باید شامل دو مقدار جدا شده با فاصله باشد"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "توکن داده شده برای هیچ نوع توکنی معتبر نمی‌باشد"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "توکن شامل هیچ شناسه قابل تشخیصی از کاربر نیست"

#: authentication.py:132
msgid "User not found"
msgstr "کاربر یافت نشد"

#: authentication.py:135
msgid "User is inactive"
msgstr "کاربر غیرفعال است"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr "رمز عبور کاربر تغییر کرده است"

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "نوع الگوریتم ناشناخته '{}'"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr "برای استفاده از {} باید رمزنگاری را نصب کرده باشید."

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr "نوع ناشناخته '{}'، 'leeway' باید از نوع int، float یا timedelta باشد."

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "توکن نامعتبر است یا منقضی شده است"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr "الگوریتم نامعتبر مشخص شده است"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "هیچ اکانت فعالی برای اطلاعات داده شده یافت نشد"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "تنظیمات '{}' حذف شده است. لطفا به '{}' برای تنظیمات موجود مراجعه کنید."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "کاربر"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "زمان ایجاد"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "زمان انقضا"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "لیست سیاه توکن"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "توکن بدون هیچ نوع و طول عمر قابل ساخت نیست"

#: tokens.py:116
msgid "Token has no id"
msgstr "توکن id ندارد"

#: tokens.py:128
msgid "Token has no type"
msgstr "توکن نوع ندارد"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "توکن نوع اشتباهی دارد"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "توکن دارای '{}' claim نمی‌باشد"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "'{}' claim توکن منقضی شده"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "توکن به لیست سیاه رفته است"
