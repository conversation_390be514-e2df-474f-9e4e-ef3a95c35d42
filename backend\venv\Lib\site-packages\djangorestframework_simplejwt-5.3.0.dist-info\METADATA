Metadata-Version: 2.1
Name: djangorestframework-simplejwt
Version: 5.3.0
Summary: A minimal JSON Web Token authentication plugin for Django REST Framework
Home-page: https://github.com/jazzband/djangorestframework-simplejwt
Author: <PERSON>
Author-email: da<PERSON><PERSON>@gmail.com
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 3.2
Classifier: Framework :: Django :: 4.0
Classifier: Framework :: Django :: 4.1
Classifier: Framework :: Django :: 4.2
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.7
License-File: LICENSE.txt
Requires-Dist: django >=3.2
Requires-Dist: djangorestframework >=3.10
Requires-Dist: pyjwt <3,>=1.7.1
Provides-Extra: crypto
Requires-Dist: cryptography >=3.3.1 ; extra == 'crypto'
Provides-Extra: dev
Requires-Dist: pytest-watch ; extra == 'dev'
Requires-Dist: wheel ; extra == 'dev'
Requires-Dist: twine ; extra == 'dev'
Requires-Dist: ipython ; extra == 'dev'
Requires-Dist: cryptography ; extra == 'dev'
Requires-Dist: pytest-cov ; extra == 'dev'
Requires-Dist: pytest-django ; extra == 'dev'
Requires-Dist: pytest-xdist ; extra == 'dev'
Requires-Dist: pytest ; extra == 'dev'
Requires-Dist: tox ; extra == 'dev'
Requires-Dist: flake8 ; extra == 'dev'
Requires-Dist: pep8 ; extra == 'dev'
Requires-Dist: isort ; extra == 'dev'
Requires-Dist: Sphinx <2,>=1.6.5 ; extra == 'dev'
Requires-Dist: sphinx-rtd-theme >=0.1.9 ; extra == 'dev'
Requires-Dist: python-jose ==3.3.0 ; extra == 'dev'
Provides-Extra: doc
Requires-Dist: Sphinx <2,>=1.6.5 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme >=0.1.9 ; extra == 'doc'
Provides-Extra: lint
Requires-Dist: flake8 ; extra == 'lint'
Requires-Dist: pep8 ; extra == 'lint'
Requires-Dist: isort ; extra == 'lint'
Provides-Extra: python-jose
Requires-Dist: python-jose ==3.3.0 ; extra == 'python-jose'
Provides-Extra: test
Requires-Dist: cryptography ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: pytest-django ; extra == 'test'
Requires-Dist: pytest-xdist ; extra == 'test'
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: tox ; extra == 'test'

Simple JWT
==========

.. image:: https://jazzband.co/static/img/badge.svg
   :target: https://jazzband.co/
   :alt: Jazzband
.. image:: https://github.com/jazzband/djangorestframework-simplejwt/workflows/Test/badge.svg
   :target: https://github.com/jazzband/djangorestframework-simplejwt/actions
   :alt: GitHub Actions
.. image:: https://codecov.io/gh/jazzband/djangorestframework-simplejwt/branch/master/graph/badge.svg
  :target: https://codecov.io/gh/jazzband/djangorestframework-simplejwt
.. image:: https://img.shields.io/pypi/v/djangorestframework-simplejwt.svg
  :target: https://pypi.python.org/pypi/djangorestframework-simplejwt
.. image:: https://img.shields.io/pypi/pyversions/djangorestframework-simplejwt.svg
  :target: https://pypi.python.org/pypi/djangorestframework-simplejwt
.. image:: https://img.shields.io/pypi/djversions/djangorestframework-simplejwt.svg
  :target: https://pypi.python.org/pypi/djangorestframework-simplejwt
.. image:: https://readthedocs.org/projects/django-rest-framework-simplejwt/badge/?version=latest
  :target: https://django-rest-framework-simplejwt.readthedocs.io/en/latest/

Abstract
--------

Simple JWT is a JSON Web Token authentication plugin for the `Django REST
Framework <http://www.django-rest-framework.org/>`__.

For full documentation, visit `django-rest-framework-simplejwt.readthedocs.io
<https://django-rest-framework-simplejwt.readthedocs.io/en/latest/>`__.


Translations
------------

Contribute translations directly with PRs or via inlang https://inlang.com/editor/github.com/jazzband/djangorestframework-simplejwt
